/* Profile-specific styles */
.animated-gradient {
  background: linear-gradient(45deg, #1e293b, #334155, #0f172a, #1e293b);
  background-size: 400% 400%;
  animation: gradientShift 12s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.network-animation {
  animation: networkPulse 4s ease-in-out infinite;
}

@keyframes networkPulse {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.4; }
}

/* Responsive layout styles */
.profile-layout {
  transition: all 0.3s ease;
}

/* Mobile-first responsive design */
@media (max-width: 640px) {
  .profile-layout {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .profile-sidebar {
    width: 100%;
    min-height: auto;
    max-height: 60vh;
    overflow-y: auto;
    border-right: none;
    border-bottom: 1px solid rgba(71, 85, 105, 0.3);
  }

  .profile-content {
    padding: 1rem;
    flex: 1;
  }

  /* Compact navigation on mobile */
  .profile-sidebar .space-y-4 > div {
    margin-bottom: 1rem;
  }

  .profile-sidebar h2 {
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
  }

  .profile-sidebar a {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .profile-layout {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .profile-sidebar {
    width: 100%;
    min-height: auto;
    max-height: 50vh;
    overflow-y: auto;
    border-right: none;
    border-bottom: 1px solid rgba(71, 85, 105, 0.3);
  }

  .profile-content {
    padding: 1.5rem;
    flex: 1;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .profile-sidebar {
    width: 280px;
  }

  .profile-content {
    padding: 2rem;
  }
}

@media (min-width: 1025px) {
  .profile-layout {
    flex-direction: row;
    height: 100vh;
  }

  .profile-sidebar {
    width: 320px;
    border-right: 1px solid rgba(71, 85, 105, 0.3);
    border-bottom: none;
  }

  .profile-content {
    padding: 2rem;
  }
}

/* Grid responsive utilities */
.profile-grid {
  display: grid;
  gap: 1.5rem;
}

@media (max-width: 640px) {
  .profile-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .profile-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.25rem;
  }
}

@media (min-width: 1025px) {
  .profile-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

/* Custom scrollbar for sidebar */
.profile-sidebar::-webkit-scrollbar {
  width: 6px;
}

.profile-sidebar::-webkit-scrollbar-track {
  background: rgba(51, 65, 85, 0.1);
}

.profile-sidebar::-webkit-scrollbar-thumb {
  background: rgba(51, 65, 85, 0.3);
  border-radius: 3px;
}

.profile-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(51, 65, 85, 0.5);
}

/* Enhanced input field styles */
.profile-input {
  transition: all 0.3s ease;
}

/* Button hover effects */
.profile-button {
  transition: all 0.3s ease;
}

/* Additional responsive utilities */
@media (max-width: 640px) {
  /* Smaller text and spacing on mobile */
  .text-3xl { font-size: 1.875rem; }
  .text-2xl { font-size: 1.5rem; }
  .text-xl { font-size: 1.25rem; }
  .text-lg { font-size: 1.125rem; }

  /* Compact buttons on mobile */
  .profile-button {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  /* Reduced margins and padding */
  .space-y-6 > * + * { margin-top: 1rem; }
  .space-y-4 > * + * { margin-top: 0.75rem; }

  /* Mobile-friendly form elements */
  input, select, textarea {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  /* Tablet adjustments */
  .profile-button {
    padding: 0.625rem 1.25rem;
    font-size: 0.9375rem;
  }
}

/* Touch-friendly interactive elements */
@media (hover: none) and (pointer: coarse) {
  .profile-sidebar a,
  .profile-button,
  button {
    min-height: 44px; /* iOS recommended touch target size */
    display: flex;
    align-items: center;
  }

  /* Remove hover effects on touch devices */
  .profile-sidebar a:hover,
  .profile-button:hover,
  button:hover {
    transform: none;
  }
}

/* Smooth section transitions */
.profile-section {
  transition: all 0.3s ease;
}
